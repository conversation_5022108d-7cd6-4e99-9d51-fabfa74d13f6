# LEAK模块 uPokeDB 命令参考文档 (新版位复用)
## 命令格式说明
### 方式1：LOAD_ACTION变量（仅限抛载控制）
```bash
uPokeDB LOAD_ACTION=true   # 开启抛载
uPokeDB LOAD_ACTION=false  # 关闭抛载
```

### 方式2：CONTROL_MSG变量（支持所有设备）
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=设备名;Power=on/off;"
```
## 抛载控制命令

### 开启抛载
```bash
# 方式1：使用LOAD_ACTION变量
uPokeDB LOAD_ACTION=true

# 方式2：使用CONTROL_MSG变量
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=LOAD;Power=on;"
```
**CAN报文**: `[0x08,0x00,0x00,0x03,0x21,0xFA,0x00,0x00,0x00,0x00,0x00,0x01,0x00,0xFA]`

### 关闭抛载
```bash
# 方式1：使用LOAD_ACTION变量
uPokeDB LOAD_ACTION=false

# 方式2：使用CONTROL_MSG变量
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=LOAD;Power=off;"
```
**CAN报文**: `[0x08,0x00,0x00,0x03,0x21,0xFA,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFA]`

## 频闪灯(LED)控制命令

### 频闪灯上电
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=LED;Power=on;"
```
**CAN报文**: `[0x08,0x00,0x00,0x03,0x21,0xFA,0x00,0x00,0x00,0x00,0x00,0x02,0x00,0xFA]`

### 频闪灯下电
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=LED;Power=off;"
```
**CAN报文**: `[0x08,0x00,0x00,0x03,0x21,0xFA,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFA]`

## 测速仪(DVL)控制命令

### DVL上电
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=DVL;Power=on;"
```
**CAN报文**: `[0x08,0x00,0x00,0x03,0x21,0xFA,0x00,0x00,0x00,0x00,0x00,0x04,0x00,0xFA]`

### DVL下电
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=DVL;Power=off;"
```
**CAN报文**: `[0x08,0x00,0x00,0x03,0x21,0xFA,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFA]`

## 避障声呐(AVOID)控制命令

### 避障声呐上电
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=AVOID;Power=on;"
```
**CAN报文**: `[0x08,0x00,0x00,0x03,0x21,0xFA,0x00,0x00,0x00,0x00,0x00,0x08,0x00,0xFA]`

### 避障声呐下电
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=AVOID;Power=off;"
```
**CAN报文**: `[0x08,0x00,0x00,0x03,0x21,0xFA,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFA]`

## 电台(RADIO)控制命令

### 电台上电
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=RADIO;Power=on;"
```
**CAN报文**: `[0x08,0x00,0x00,0x03,0x21,0xFA,0x00,0x00,0x00,0x00,0x00,0x10,0x00,0xFA]`

### 电台下电
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=RADIO;Power=off;"
```
**CAN报文**: `[0x08,0x00,0x00,0x03,0x21,0xFA,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFA]`

## 水声通讯(HYP)控制命令

### 水声通讯上电
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=HYP;Power=on;"
```
**CAN报文**: `[0x08,0x00,0x00,0x03,0x21,0xFA,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0xFA]`

### 水声通讯下电
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=HYP;Power=off;"
```
**CAN报文**: `[0x08,0x00,0x00,0x03,0x21,0xFA,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFA]`

## 载荷低压(AEMLOADLOW)控制命令

### 载荷低压上电
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=AEMLOADLOW;Power=on;"
```
**CAN报文**: `[0x08,0x00,0x00,0x03,0x21,0xFA,0x00,0x00,0x00,0x00,0x00,0x00,0x02,0xFA]`

### 载荷低压下电
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=AEMLOADLOW;Power=off;"
```
**CAN报文**: `[0x08,0x00,0x00,0x03,0x21,0xFA,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFA]`

## 超短基线定位(USBL)控制命令

### USBL上电
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=USBL;Power=on;"
```
**CAN报文**: `[0x08,0x00,0x00,0x03,0x21,0xFA,0x00,0x00,0x00,0x00,0x00,0x00,0x04,0xFA]`

### USBL下电
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=USBL;Power=off;"
```
**CAN报文**: `[0x08,0x00,0x00,0x03,0x21,0xFA,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFA]`

## 点火炬(TORCH)控制命令

### 点火炬上电
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=TORCH;Power=on;"
```
**CAN报文**: `[0x08,0x00,0x00,0x03,0x21,0xFA,0x00,0x00,0x00,0x00,0x00,0x00,0x08,0xFA]`

### 点火炬下电
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=TORCH;Power=off;"
```
**CAN报文**: `[0x08,0x00,0x00,0x03,0x21,0xFA,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFA]`

## 组合设备控制命令

### 所有设备上电
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=ALL;Power=on;"
```
**CAN报文**: `[0x08,0x00,0x00,0x03,0x21,0xFA,0x00,0x00,0x00,0x00,0x00,0x1F,0x0F,0xFA]`
**说明**: COM7=0x1F(所有5个设备), COM8=0x0F(所有4个设备)

### 所有设备下电
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=ALL;Power=off;"
```
**CAN报文**: `[0x08,0x00,0x00,0x03,0x21,0xFA,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFA]`

## CAN报文格式说明

### 13字节网络帧结构
```
[0x08, 0x00, 0x00, 0x03, 0x21, 0xFA, 保留1, 保留2, 保留3, 保留4, 保留5, COM7, COM8, 0xFA]
 |     |     |     |     |     |     |      |      |      |      |      |     |     |
 |     |     |     |     |     |     |      |      |      |      |      |     |     帧尾标识
 |     |     |     |     |     |     |      |      |      |      |      |     COM8控制字节
 |     |     |     |     |     |     |      |      |      |      |      COM7控制字节
 |     |     |     |     |     |     |      |      |      |      保留字节5
 |     |     |     |     |     |     |      |      |      保留字节4
 |     |     |     |     |     |     |      |      保留字节3
 |     |     |     |     |     |     |      保留字节2
 |     |     |     |     |     |     保留字节1
 |     |     |     |     |     帧头标识(0xFA)
 |     |     |     |     CAN ID (0x321)
 |     |     |     网络帧头
 网络帧长度标识
```

### 新设备值映射表
| 设备 | COM口 | 位位置 | 开启值 | 关闭值 | 在帧中位置 |
|------|-------|--------|--------|--------|------------|
| LOAD | COM7 | 位0 | 0x01 | 0x00 | Frame[11] |
| LED | COM7 | 位1 | 0x02 | 0x00 | Frame[11] |
| DVL | COM7 | 位2 | 0x04 | 0x00 | Frame[11] |
| AVOID | COM7 | 位3 | 0x08 | 0x00 | Frame[11] |
| RADIO | COM7 | 位4 | 0x10 | 0x00 | Frame[11] |
| HYP | COM8 | 位0 | 0x01 | 0x00 | Frame[12] |
| AEMLOADLOW | COM8 | 位1 | 0x02 | 0x00 | Frame[12] |
| USBL | COM8 | 位2 | 0x04 | 0x00 | Frame[12] |
| TORCH | COM8 | 位3 | 0x08 | 0x00 | Frame[12] |

### 位复用示例
**同时开启抛载和频闪**：
```bash
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=LOAD;Power=on;"
uPokeDB CONTROL_MSG="MsgType=control;Act=sensor;Type=LED;Power=on;"
```
**结果**: COM7 = 0x01 | 0x02 = 0x03

## 使用注意事项

1. **LOAD_ACTION变量**仅支持抛载控制，其他设备必须使用CONTROL_MSG
2. **CONTROL_MSG格式**必须严格按照`MsgType=control;Act=sensor;Type=设备名;Power=on/off;`
3. **设备名称**区分大小写，必须使用大写字母
4. **位复用机制**：同一COM口的多个设备可以同时开启，控制值会进行OR运算
5. **ALL命令**：会控制所有9个设备
6. **新增设备**：RADIO(电台)和TORCH(点火炬)

## 接收报文处理

LEAK模块不仅能发送控制命令，也能接收其他模块发送的0x321控制帧并解析设备状态。

### 接收帧格式
当LEAK模块接收到CAN ID为0x321的13字节控制帧时，会按照新的位复用方式解析：

```
接收帧: [0x08,0x00,0x00,0x03,0x21,0xFA,保留,保留,保留,保留,保留,COM7,COM8,0xFA]
```

### 设备状态解析
- **COM7字节 (Frame[11])**: 解析5个设备状态
  - 位0 (0x01): 抛载状态
  - 位1 (0x02): 频闪状态
  - 位2 (0x04): DVL状态
  - 位3 (0x08): 避碰状态
  - 位4 (0x10): 电台状态

- **COM8字节 (Frame[12])**: 解析4个设备状态
  - 位0 (0x01): 水声状态
  - 位1 (0x02): 载荷低压状态
  - 位2 (0x04): USBL状态
  - 位3 (0x08): 点火炬状态

### 接收示例
**接收到载荷低压开启命令**：
```
接收帧: [0x08,0x00,0x00,0x03,0x21,0xFA,0x00,0x00,0x00,0x00,0x00,0x00,0x02,0xFA]
解析结果: COM8=0x02, 载荷低压状态=ON
```

**接收到抛载和DVL同时开启**：
```
接收帧: [0x08,0x00,0x00,0x03,0x21,0xFA,0x00,0x00,0x00,0x00,0x00,0x05,0x00,0xFA]
解析结果: COM7=0x05 (0x01|0x04), 抛载=ON, DVL=ON
```

## 状态查询

LEAK模块会发布以下MOOS变量供状态查询：

### 设备状态变量
- `LOAD_STATUS`：抛载状态
- `LED_STATUS`：频闪状态
- `DVL_STATUS`：DVL状态
- `AVOID_STATUS`：避碰状态
- `RADIO_STATUS`：电台状态
- `HYP_STATUS`：水声状态
- `AEMLOADLOW_STATUS`：载荷低压状态
- `USBL_STATUS`：USBL状态
- `TORCH_STATUS`：点火炬状态

### 系统状态变量
- `DEVICE_CONTROL_RECEIVED`：设备控制命令接收确认
- `LEAK`：综合漏水状态
- `LEAK_CODE`：漏水故障码
